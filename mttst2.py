import os
import numpy as np
import warnings

from PySide6 import Qt<PERSON>ore
from ultralytics import YOLO
import onnxruntime as ort
import torch
from lib.core.general import non_max_suppression
import cv2
import PySimpleGUIQt as sg
import threading
from queue import Queue
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 鍏ㄥ眬閰嶇疆
MAX_QUEUE_SIZE = 30  # 闃熷垪鏈€澶у�归噺
NUM_WORKERS = 1  # 澶勭悊绾跨▼鏁�
PROGRESS_UPDATE_INTERVAL = 0.5  # 杩涘害鏇存柊闂撮殧(绉�)

# 鍏变韩闃熷垪鍜屼簨浠�
input_queue = Queue(maxsize=MAX_QUEUE_SIZE)
output_queue = Queue(maxsize=MAX_QUEUE_SIZE)
stop_event = threading.Event()


class YOLOP_ONNX:
    def __init__(self, weight_path="YOLOP-main/weights/yolop-640-640.onnx"):
        ort.set_default_logger_severity(4)
        providers = ort.get_available_providers()
        if 'CUDAExecutionProvider' in providers:
            provider = ['CUDAExecutionProvider']
        else:
            provider = ['CPUExecutionProvider']
        self.ort_session = ort.InferenceSession(weight_path, providers=provider)

    def _resize_unscale(self, img, new_shape=(640, 640), color=114):
        shape = img.shape[:2]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        canvas = np.zeros((new_shape[0], new_shape[1], 3))
        canvas.fill(color)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])

        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        new_unpad_w, new_unpad_h = new_unpad
        pad_w, pad_h = new_shape[1] - new_unpad_w, new_shape[0] - new_unpad_h
        dw = pad_w // 2
        dh = pad_h // 2

        if shape[::-1] != new_unpad:
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_AREA)

        canvas[dh:dh + new_unpad_h, dw:dw + new_unpad_w, :] = img
        return canvas, r, dw, dh, new_unpad_w, new_unpad_h

    def process_image(self, img_bgr):
        height, width, _ = img_bgr.shape
        img_rgb = img_bgr[:, :, ::-1].copy()

        canvas, r, dw, dh, new_unpad_w, new_unpad_h = self._resize_unscale(img_rgb, (640, 640))

        img = canvas.copy().astype(np.float32)
        img /= 255.0
        img[:, :, 0] -= 0.485
        img[:, :, 1] -= 0.456
        img[:, :, 2] -= 0.406
        img[:, :, 0] /= 0.229
        img[:, :, 1] /= 0.224
        img[:, :, 2] /= 0.225

        img = img.transpose(2, 0, 1)
        img = np.expand_dims(img, 0)

        det_out, da_seg_out, ll_seg_out = self.ort_session.run(
            ['det_out', 'drive_area_seg', 'lane_line_seg'],
            input_feed={"images": img}
        )

        det_out = torch.from_numpy(det_out).float()
        conf_thres = 0.6
        iou_thres = 0.65
        boxes = non_max_suppression(det_out, conf_thres=conf_thres, iou_thres=iou_thres)[0]
        boxes = boxes.cpu().numpy().astype(np.float32)

        results = {'detection': None, 'da_seg': None, 'll_seg': None, 'merged': None}

        img_det = img_bgr.copy()
        if boxes.shape[0] > 0:
            boxes[:, 0] -= dw
            boxes[:, 1] -= dh
            boxes[:, 2] -= dw
            boxes[:, 3] -= dh
            boxes[:, :4] /= r

            for i in range(boxes.shape[0]):
                x1, y1, x2, y2, conf, label = boxes[i]
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                img_det = cv2.rectangle(img_det, (x1, y1), (x2, y2), (0, 255, 0), 2)

        results['detection'] = img_det

        da_seg_out = da_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]
        ll_seg_out = ll_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]

        da_seg_mask = np.argmax(da_seg_out, axis=1)[0]
        ll_seg_mask = np.argmax(ll_seg_out, axis=1)[0]

        def calculate_center_line(left_points, right_points, y_range=None, step=10, poly_order=2):
            if len(left_points) < 3 or len(right_points) < 3:
                return []

            left_arr = np.array(left_points)
            right_arr = np.array(right_points)

            with warnings.catch_warnings():
                warnings.simplefilter('ignore', RuntimeWarning)
                left_fit = np.polyfit(left_arr[:, 1], left_arr[:, 0], poly_order)
                right_fit = np.polyfit(right_arr[:, 1], right_arr[:, 0], poly_order)

            if y_range is None:
                y_min = max(np.min(left_arr[:, 1]), np.min(right_arr[:, 1]))
                y_max = min(np.max(left_arr[:, 1]), np.max(right_arr[:, 1]))
            else:
                y_min, y_max = y_range

            y_min = min(np.min(left_arr[:, 1]), np.min(right_arr[:, 1]))
            y_max = max(np.max(left_arr[:, 1]), np.max(right_arr[:, 1]))

            y_samples = np.arange(y_max, y_min, -step)
            center_fit = (left_fit + right_fit) / 2
            center_points = []

            for y in y_samples:
                x_left = np.polyval(left_fit, y)
                x_right = np.polyval(right_fit, y)
                x_center = np.polyval(center_fit, y)
                x_center = np.clip(x_center, x_left, x_right)
                center_points.append((x_center, y))

            return center_points

        def calculate_lane_lines_by_rays(mask, ray_step=4, search_range=150):
            h, w = mask.shape
            center_x = w // 2
            left_points = []
            right_points = []

            for y in range(h - 1, 0, -ray_step):
                if center_x - search_range < 1 or center_x + search_range > w - 1:
                    center_x = w // 2

                left_x = None
                for x in range(center_x, max(0, center_x - search_range), -1):
                    if mask[y, x] > 0:
                        left_x = x
                        break

                right_x = None
                for x in range(center_x, min(w, center_x + search_range)):
                    if mask[y, x] > 0:
                        right_x = x
                        break

                if left_x is not None:
                    left_points.append((left_x, y))
                if right_x is not None:
                    right_points.append((right_x, y))

                if left_x is not None and right_x is not None:
                    center_x = (left_x + right_x) // 2
                elif left_x is not None:
                    center_x = left_x + search_range
                elif right_x is not None:
                    center_x = right_x - search_range

            def fit_poly(points, order):
                if len(points) < order + 1:
                    return np.zeros(order + 1)
                arr = np.array(points)
                return np.polyfit(arr[:, 1], arr[:, 0], order)

            left_coeffs = fit_poly(left_points, 2)
            right_coeffs = fit_poly(right_points, 2)
            max_len = max(len(left_coeffs), len(right_coeffs))
            left_coeffs = np.pad(left_coeffs, (0, max_len - len(left_coeffs)), 'constant')
            right_coeffs = np.pad(right_coeffs, (0, max_len - len(right_coeffs)), 'constant')
            center_coeffs = (left_coeffs + right_coeffs) / 2

            y_values = np.linspace(0, h - 1, 20)
            center_x = np.polyval(center_coeffs[::-1], y_values)
            center_points = np.column_stack((center_x, y_values)).astype(int).tolist()

            return left_points, right_points, center_points

        def fit_lane_polynomials(left_points, right_points, poly_order=2):
            left_arr = np.array(left_points)
            right_arr = np.array(right_points)

            if len(left_arr) >= poly_order + 1:
                left_coeffs = np.polyfit(left_arr[:, 1], left_arr[:, 0], poly_order)
            else:
                left_coeffs = np.zeros(poly_order + 1)

            if len(right_arr) >= poly_order + 1:
                right_coeffs = np.polyfit(right_arr[:, 1], right_arr[:, 0], poly_order)
            else:
                right_coeffs = np.zeros(poly_order + 1)

            max_len = max(len(left_coeffs), len(right_coeffs))
            left_coeffs = np.pad(left_coeffs, (0, max_len - len(left_coeffs)), 'constant')
            right_coeffs = np.pad(right_coeffs, (0, max_len - len(right_coeffs)), 'constant')
            center_coeffs = (left_coeffs + right_coeffs) / 2

            return left_coeffs, right_coeffs, center_coeffs

        left_lane, right_lane, center_lane = calculate_lane_lines_by_rays(ll_seg_mask)
        h, w = ll_seg_mask.shape
        center_lane = calculate_center_line(
            left_lane,
            right_lane,
            y_range=(h * 0.3, h),
            step=5,
            poly_order=2
        )
        left_coeffs, right_coeffs, center_coeffs = fit_lane_polynomials(left_lane, right_lane)
        y_values = np.linspace(0, ll_seg_mask.shape[0] - 1, 20)

        color_area = np.zeros((new_unpad_h, new_unpad_w, 3), dtype=np.uint8)
        color_area[da_seg_mask == 1] = [0, 255, 0]
        color_area[ll_seg_mask == 1] = [255, 0, 0]

        ll_seg_color = np.zeros((new_unpad_h, new_unpad_w, 3), dtype=np.uint8)
        ll_seg_color[ll_seg_mask == 1] = [255, 0, 0]
        color_area = cv2.addWeighted(color_area, 1.0, ll_seg_color, 1.0, 0)

        if len(left_lane) > 1:
            left_lane = np.array(left_lane, np.int32)
            cv2.polylines(color_area, [left_lane], False, (255, 255, 0), 2)

        if len(right_lane) > 1:
            right_lane = np.array(right_lane, np.int32)
            cv2.polylines(color_area, [right_lane], False, (0, 255, 255), 2)

        if len(center_lane) > 1:
            center_lane = np.array(center_lane, np.int32)
            cv2.polylines(color_area, [center_lane], False, (255, 0, 255), 2)

        color_seg = color_area[..., ::-1]
        color_mask = np.mean(color_seg, 2)

        img_merge = canvas[dh:dh + new_unpad_h, dw:dw + new_unpad_w, :]
        img_merge = img_merge[:, :, ::-1]
        img_merge[color_mask != 0] = img_merge[color_mask != 0] * 0.5 + color_seg[color_mask != 0] * 0.5
        img_merge = img_merge.astype(np.uint8)

        if boxes.shape[0] > 0:
            boxes_canvas = np.zeros_like(img_merge)
            scale_w = new_unpad_w / width
            scale_h = new_unpad_h / height

            for i in range(boxes.shape[0]):
                x1, y1, x2, y2, conf, label = boxes[i]
                x1 = int(x1 * scale_w)
                y1 = int(y1 * scale_h)
                x2 = int(x2 * scale_w)
                y2 = int(y2 * scale_h)
                boxes_canvas = cv2.rectangle(boxes_canvas, (x1, y1), (x2, y2), (0, 255, 0), 2)

            img_merge = cv2.addWeighted(img_merge, 1.0, boxes_canvas, 1.0, 0)

        da_seg_out = da_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]
        ll_seg_out = ll_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]

        da_seg_mask = np.argmax(da_seg_out, axis=1)[0].astype(np.uint8)
        ll_seg_mask = np.argmax(ll_seg_out, axis=1)[0].astype(np.uint8)

        da_seg_mask = cv2.resize((da_seg_mask * 255).astype(np.uint8), (width, height), interpolation=cv2.INTER_LINEAR)
        ll_seg_mask = cv2.resize((ll_seg_mask * 255).astype(np.uint8), (width, height), interpolation=cv2.INTER_LINEAR)

        results['left_lane'] = left_lane
        results['right_lane'] = right_lane
        results['center_lane'] = center_lane
        results['da_seg'] = da_seg_mask
        results['ll_seg'] = ll_seg_mask
        results['merged'] = img_merge

        return results


class VideoProcessor:
    def __init__(self):
        self.yolop_model = YOLOP_ONNX("YOLOP-main/weights/yolop-640-640.onnx")
        self.yolov8_model = YOLO("YOLOv8_GUI_For_Traffic_Sign_Detection-main/ptfolder/best.pt")
        self.yolov8_model.to('cuda' if torch.cuda.is_available() else 'cpu')

    def process_frame(self, frame):
        try:
            yolop_results = self.yolop_model.process_image(frame)

            yolov8_results = self.yolov8_model.predict(
                source=yolop_results['merged'],
                conf=0.4,
                save=False,
                show_labels=True,
                show_conf=True
            )

            return yolov8_results[0].plot()
        except Exception as e:
            print(f"甯у�勭悊閿欒��: {e}")
            return None


def video_reader(video_path, progress_callback=None):
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"鏃犳硶鎵撳紑瑙嗛�戞枃浠�: {video_path}")

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        frame_count = 0
        last_update_time = 0

        while not stop_event.is_set():
            ret, frame = cap.read()
            if not ret:
                break

            # 鏀逛负闃诲�炲紡绛夊緟闃熷垪绌洪棿 猸�
            while input_queue.qsize() >= MAX_QUEUE_SIZE and not stop_event.is_set():
                time.sleep(0.1)
                if progress_callback:
                    progress_callback(frame_count / total_frames * 100)  # 淇濇寔杩涘害鏇存柊

            input_queue.put((frame_count, frame))
            frame_count += 1

            current_time = time.time()
            if progress_callback and (current_time - last_update_time) > PROGRESS_UPDATE_INTERVAL:
                progress = frame_count / total_frames * 100
                progress_callback(progress)
                last_update_time = current_time

        if progress_callback:
            progress_callback(100)

    except Exception as e:
        print(f"瑙嗛�戣�诲彇閿欒��: {e}")
    finally:
        cap.release()
        print("瑙嗛�戣�诲彇绾跨▼缁撴潫")


def frame_worker(worker_id):
    processor = VideoProcessor()

    while not stop_event.is_set():
        try:
            try:
                frame_id, frame = input_queue.get(timeout=0.1)
            except:
                continue

            processed_frame = processor.process_frame(frame)

            if processed_frame is not None:
                output_queue.put((frame_id, processed_frame))

            input_queue.task_done()

        except Exception as e:
            print(f"宸ヤ綔绾跨▼ {worker_id} 閿欒��: {e}")
            continue


def video_writer(output_path, original_video_path, total_frames, progress_callback=None):
    try:
        cap = cv2.VideoCapture(original_video_path)
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()

        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        frame_buffer = {}
        next_frame_id = 0
        last_update_time = 0

        while not stop_event.is_set() and (next_frame_id < total_frames or not output_queue.empty()):
            try:
                try:
                    frame_id, frame = output_queue.get(timeout=0.1)
                except:
                    continue

                frame_buffer[frame_id] = frame

                while next_frame_id in frame_buffer:
                    out.write(frame_buffer.pop(next_frame_id))
                    next_frame_id += 1

                    current_time = time.time()
                    if progress_callback and (current_time - last_update_time) > PROGRESS_UPDATE_INTERVAL:
                        progress = next_frame_id / total_frames * 100
                        progress_callback(progress)
                        last_update_time = current_time

                output_queue.task_done()

            except Exception as e:
                print(f"瑙嗛�戝啓鍏ラ敊璇�: {e}")
                continue

        while next_frame_id < total_frames and next_frame_id in frame_buffer:
            out.write(frame_buffer.pop(next_frame_id))
            next_frame_id += 1

        if progress_callback:
            progress_callback(100)

    except Exception as e:
        print(f"瑙嗛�戝啓鍏ョ嚎绋嬮敊璇�: {e}")
    finally:
        out.release()
        print("瑙嗛�戝啓鍏ョ嚎绋嬬粨鏉�")


def process_video_parallel(input_video, output_path):
    cap = cv2.VideoCapture(input_video)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    cap.release()

    progress_layout = [
        [sg.Text("澶勭悊杩涘害: 0%", key="-PROGRESS_TEXT-")],
        [sg.ProgressBar(100, orientation='h', size=(40, 20), key="-PROGRESS-")]
    ]
    progress_window = sg.Window("澶勭悊杩涘害", progress_layout, finalize=True)
    if hasattr(progress_window, 'QTroot'):
        progress_window.QTroot.setWindowFlags(progress_window.QTroot.windowFlags() | QtCore.Qt.WindowStaysOnTopHint)

    def update_progress(progress):
        progress_window["-PROGRESS-"].update(progress)
        progress_window["-PROGRESS_TEXT-"].update(f"澶勭悊杩涘害: {progress:.1f}%")
        progress_window.read(timeout=10)

    with ThreadPoolExecutor(max_workers=NUM_WORKERS + 2) as executor:
        reader_future = executor.submit(video_reader, input_video, update_progress)
        writer_future = executor.submit(video_writer, output_path, input_video, total_frames, update_progress)

        worker_futures = [executor.submit(frame_worker, i) for i in range(NUM_WORKERS)]

        try:
            for future in as_completed([reader_future] + worker_futures + [writer_future]):
                future.result()

        except Exception as e:
            print(f"澶勭悊杩囩▼涓�鍙戠敓閿欒��: {e}")
            stop_event.set()

        finally:
            progress_window.close()
            sg.popup(f"瑙嗛�戝�勭悊瀹屾垚! 宸蹭繚瀛樺埌: {output_path}", title="瀹屾垚")


def browse_input_file():
    file_path = sg.popup_get_file(
        "閫夋嫨杈撳叆 MP4 瑙嗛��",
        file_types=(("MP4 鏂囦欢", "*.mp4"),),
        no_window=True
    )
    return file_path if file_path else None


def browse_output_path(input_video):
    default_name = os.path.splitext(os.path.basename(input_video))[0] + "_output.mp4"

    layout = [
        [sg.Text("杈撳嚭鐩�褰�:"), sg.Input(key="-FOLDER-"), sg.FolderBrowse("娴忚��")],
        [sg.Text("杈撳嚭鏂囦欢鍚�:"), sg.Input(default_name, key="-NAME-")],
        [sg.Button("纭�璁�"), sg.Button("鍙栨秷")]
    ]

    window = sg.Window("閫夋嫨杈撳嚭浣嶇疆", layout)

    while True:
        event, values = window.read()
        if event in (sg.WINDOW_CLOSED, "鍙栨秷"):
            output_path = None
            break
        elif event == "纭�璁�":
            folder = values["-FOLDER-"]
            name = values["-NAME-"]
            if not folder or not name:
                sg.popup_error("璇峰～鍐欒緭鍑虹洰褰曞拰鏂囦欢鍚嶏紒")
                continue
            output_path = os.path.join(folder, name)
            break

    window.close()
    return output_path


# def main():
#     cap = cv2.VideoCapture(0)
#     if not cap.isOpened():
#         print("鏃犳硶鎵撳紑鎽勫儚澶�")
#         return
#
#     cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
#     cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
#
#     cv2.namedWindow('鍘熷�嬭�嗛��', cv2.WINDOW_NORMAL)
#     cv2.namedWindow('澶勭悊鍚庣殑瑙嗛��', cv2.WINDOW_NORMAL)
#
#     processor = VideoProcessor()
#
#     try:
#         while True:
#             ret, frame = cap.read()
#             if not ret:
#                 print("鏃犳硶鑾峰彇甯�")
#                 break
#
#             cv2.imshow('鍘熷�嬭�嗛��', frame)
#
#             processed_frame = processor.process_frame(frame)
#             if processed_frame is not None:
#                 cv2.imshow('澶勭悊鍚庣殑瑙嗛��', processed_frame)
#
#             key = cv2.waitKey(1)
#             if key == 27 or key & 0xFF == ord('q'):
#                 break
#
#     finally:
#         cap.release()
#         cv2.destroyAllWindows()
def main():
    cap = cv2.VideoCapture(0)
    processor = VideoProcessor()

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 闈為樆濉炲紡澶勭悊
        processed_frame = processor.process_frame(frame)

        if processed_frame is not None:
            cv2.imshow('澶勭悊鍚庣殑瑙嗛��', processed_frame)

        # 蹇呴』娣诲姞姝ょ瓑寰� 猸�
        key = cv2.waitKey(1) & 0xFF
        if key == 27 or key == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

layout = [
    [
        sg.Button("1. 閫夋嫨杈撳叆瑙嗛��", key="-INPUT-", size=(20, 3)),
        sg.Button("2. 瀹炴椂妫€娴�", key="-MAIN-", size=(20, 3))
    ],
    [sg.Text("鎷栨斁 MP4 鏂囦欢鍒版寜閽�1", text_color="gray", justification="center")]
]

window = sg.Window("瑙嗛�戝�勭悊宸ュ叿", layout, finalize=True)
window["-INPUT-"].Widget.setAcceptDrops(True)

input_video = None
while True:
    event, values = window.read()

    if event == sg.WINDOW_CLOSED:
        break
    elif event == "-MAIN-":
        main()
    elif event == "-INPUT-":
        input_video = browse_input_file()
        if input_video:
            output_path = browse_output_path(input_video)
            if output_path:
                stop_event.clear()
                process_video_parallel(input_video, output_path)
    elif event == "-INPUT-+DROP-":
        dropped_files = values["-INPUT-+DROP-"]
        if dropped_files:
            input_video = dropped_files[0]
            if input_video.lower().endswith(".mp4"):
                output_path = browse_output_path(input_video)
                if output_path:
                    stop_event.clear()
                    process_video_parallel(input_video, output_path)
            else:
                sg.popup_error("璇锋嫋鏀� .MP4 鏂囦欢锛�")

window.close()