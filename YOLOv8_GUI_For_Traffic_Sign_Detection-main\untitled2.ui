<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>300</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QGridLayout" name="gridLayout">
     <item row="2" column="1">
      <widget class="DoubleSpinBox" name="confSpinBox">
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.010000000000000</double>
       </property>
       <property name="stepType">
        <enum>QAbstractSpinBox::DefaultStepType</enum>
       </property>
       <property name="value">
        <double>0.250000000000000</double>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="SpinBox" name="numSpinBox">
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="maximum">
        <number>300</number>
       </property>
       <property name="value">
        <number>300</number>
       </property>
      </widget>
     </item>
     <item row="6" column="0" colspan="2">
      <widget class="QPushButton" name="resetOldSettingButton">
       <property name="text">
        <string>恢复默认</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="confLabel">
       <property name="text">
        <string>置信度</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="iouLabel">
       <property name="text">
        <string>交并比</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item row="3" column="0" colspan="2">
      <widget class="Slider" name="confSlider">
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>25</number>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="tickPosition">
        <enum>QSlider::TicksAbove</enum>
       </property>
       <property name="tickInterval">
        <number>10</number>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="numLabel">
       <property name="text">
        <string>最大检测数量</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item row="1" column="0" colspan="2">
      <widget class="Slider" name="iouSlider">
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>70</number>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="tickPosition">
        <enum>QSlider::TicksAbove</enum>
       </property>
       <property name="tickInterval">
        <number>10</number>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="DoubleSpinBox" name="iouSpinBox">
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.010000000000000</double>
       </property>
       <property name="stepType">
        <enum>QAbstractSpinBox::DefaultStepType</enum>
       </property>
       <property name="value">
        <double>0.700000000000000</double>
       </property>
      </widget>
     </item>
     <item row="5" column="0" colspan="2">
      <widget class="Slider" name="numSlider">
       <property name="maximum">
        <number>300</number>
       </property>
       <property name="value">
        <number>300</number>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="tickPosition">
        <enum>QSlider::TicksAbove</enum>
       </property>
       <property name="tickInterval">
        <number>10</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>Slider</class>
   <extends>QSlider</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>DoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>SpinBox</class>
   <extends>QSpinBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
