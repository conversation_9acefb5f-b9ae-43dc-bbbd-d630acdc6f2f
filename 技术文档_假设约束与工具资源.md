# 智能交通视觉检测系统技术文档

## 3. 假设和约束

### 3.1 重要假设

#### 3.1.1 硬件假设
- **GPU加速支持**: 系统假设运行环境具备CUDA兼容的GPU，用于深度学习模型推理加速
- **摄像头规格**: 实时检测模式假设摄像头分辨率至少为640x480，帧率≥30fps
- **内存要求**: 系统假设可用内存≥8GB，用于模型加载和视频处理缓存

#### 3.1.2 环境假设  
- **光照条件**: 系统在正常日光和夜间路灯照明条件下工作最佳
- **天气条件**: 假设在晴天、阴天和小雨条件下正常工作，暴雨和大雾可能影响检测精度
- **道路类型**: 主要针对城市道路和高速公路场景，乡村道路可能存在适应性问题

#### 3.1.3 数据假设
- **车道线标准**: 假设车道线符合国际标准，宽度为10-15cm的白色或黄色实线/虚线
- **交通标志规范**: 基于TT100K2021数据集的45类交通标志，符合中国交通标志标准
- **视频格式**: 输入视频假设为MP4格式，编码为H.264

### 3.2 技术约束

#### 3.2.1 性能约束
- **实时处理**: 实时模式下处理延迟需控制在100ms以内
- **检测精度**: 目标检测mAP≥76.5%，车道线检测IoU≥26.2%
- **内存占用**: 单帧处理内存峰值不超过2GB

#### 3.2.2 模型约束
- **输入尺寸**: YOLOP模型固定输入尺寸640x640，需要图像预处理
- **置信度阈值**: 目标检测置信度阈值设为0.6，IoU阈值0.65
- **多任务平衡**: 需要在检测精度和推理速度间权衡

#### 3.2.3 系统约束
- **并发限制**: 视频处理队列最大容量30帧，防止内存溢出
- **文件格式**: 仅支持MP4视频输入，输出格式固定为MP4
- **GUI框架**: 基于PySide6和PySimpleGUIQt，跨平台兼容性有限

### 3.3 约束对开发的影响

#### 3.3.1 架构设计影响
- **多线程架构**: 由于实时性要求，采用生产者-消费者模式的多线程架构
- **内存管理**: 实现帧缓存队列和动态内存释放机制
- **错误处理**: 需要完善的异常处理机制应对硬件故障

#### 3.3.2 算法优化影响  
- **模型量化**: 考虑使用ONNX Runtime进行模型优化
- **预处理优化**: 图像缩放和归一化需要高效实现
- **后处理简化**: 非极大值抑制和坐标变换需要向量化计算

#### 3.3.3 用户体验影响
- **进度反馈**: 视频处理需要实时进度显示
- **拖拽支持**: GUI需要支持文件拖拽操作
- **错误提示**: 需要友好的中文错误提示信息

## 4. 确定潜在的工具和资源

### 4.1 软件工具

#### 4.1.1 深度学习框架
- **PyTorch**: 主要深度学习框架，版本≥1.7.0
- **ONNX Runtime**: 模型推理优化，支持CPU/GPU加速
- **Ultralytics YOLO**: YOLOv8交通标志检测模型

#### 4.1.2 计算机视觉库
- **OpenCV**: 图像处理和视频I/O，版本≥4.5.0
- **NumPy**: 数值计算和数组操作，版本≥1.19.0
- **PIL/Pillow**: 图像格式转换和基础处理

#### 4.1.3 GUI开发工具
- **PySide6**: 主要GUI框架，提供现代化界面
- **PySimpleGUIQt**: 简化的GUI组件，用于快速原型
- **QFluentWidgets**: 界面美化组件库

### 4.2 硬件和物理工具

#### 4.2.1 计算硬件
- **GPU**: NVIDIA RTX系列或GTX 1060以上，显存≥6GB
- **CPU**: Intel i5-8代以上或AMD Ryzen 5以上
- **内存**: DDR4 16GB以上，推荐32GB

#### 4.2.2 采集设备
- **USB摄像头**: 分辨率1080p，帧率30fps以上
- **车载摄像头**: 广角镜头，具备夜视功能
- **存储设备**: SSD硬盘，读写速度≥500MB/s

### 4.3 开发工具

#### 4.3.1 集成开发环境
- **PyCharm Professional**: 主要Python IDE
- **Visual Studio Code**: 轻量级代码编辑器
- **Jupyter Notebook**: 算法原型开发和调试

#### 4.3.2 版本控制工具
- **Git**: 代码版本管理
- **GitHub/GitLab**: 远程仓库托管
- **Git LFS**: 大文件（模型权重）版本控制

#### 4.3.3 调试和性能分析
- **cProfile**: Python性能分析
- **NVIDIA Nsight**: GPU性能监控
- **Memory Profiler**: 内存使用分析

### 4.4 团队协调和规划工具

#### 4.4.1 项目管理
- **Jira**: 任务跟踪和Bug管理
- **Confluence**: 技术文档协作
- **Slack/钉钉**: 团队即时通讯

#### 4.4.2 文档工具
- **Markdown**: 技术文档编写
- **Draw.io**: 系统架构图绘制
- **Notion**: 知识库管理

### 4.5 数据源

#### 4.5.1 训练数据集
- **BDD100K**: 自动驾驶数据集，包含检测、分割标注
- **TT100K2021**: 中国交通标志数据集，45类标志
- **CULane**: 车道线检测专用数据集

#### 4.5.2 测试数据
- **自采集视频**: 本地道路实际采集数据
- **公开测试集**: 标准化评估数据
- **合成数据**: 特殊场景模拟数据

### 4.6 建模方法

#### 4.6.1 多任务学习
- **YOLOP架构**: 端到端多任务学习框架
- **特征共享**: 编码器共享，多个解码头
- **损失函数**: 加权多任务损失

#### 4.6.2 后处理算法
- **非极大值抑制**: 目标检测去重
- **多项式拟合**: 车道线平滑化
- **射线法检测**: 车道线边界提取

## 5. 交付

### 5.1 相对于完整产品的可交付成果描述

#### 5.1.1 核心功能模块 (已完成80%)
- **实时视觉检测系统**: 支持摄像头实时检测，延迟<100ms
- **视频批处理系统**: 支持MP4视频文件批量处理
- **多任务检测引擎**: 集成目标检测、车道线检测、可行驶区域分割

#### 5.1.2 用户界面 (已完成90%)
- **图形化操作界面**: 基于PySide6的现代化GUI
- **拖拽文件支持**: 支持MP4文件直接拖拽处理
- **进度显示系统**: 实时显示处理进度和状态

#### 5.1.3 性能优化 (已完成70%)
- **GPU加速推理**: 支持CUDA和CPU双模式
- **多线程处理**: 生产者-消费者模式提升效率
- **内存管理**: 队列缓存和动态释放机制

### 5.2 成功投放的指标

#### 5.2.1 技术性能指标
- **检测精度**: 目标检测mAP达到76.5%以上
- **处理速度**: 实时模式帧率≥25fps，视频处理速度≥实时播放
- **系统稳定性**: 连续运行8小时无崩溃，内存泄漏<100MB/小时

#### 5.2.2 用户体验指标  
- **界面响应**: GUI操作响应时间<200ms
- **错误处理**: 异常情况下有明确中文提示，系统自动恢复
- **易用性**: 新用户5分钟内完成基本操作

#### 5.2.3 兼容性指标
- **硬件兼容**: 支持主流GPU(NVIDIA GTX 1060+)和CPU模式
- **系统兼容**: Windows 10/11, Ubuntu 18.04+
- **文件格式**: 支持常见MP4编码格式，输出质量无明显损失

#### 5.2.4 部署指标
- **安装便捷性**: 一键安装脚本，依赖自动解决
- **资源占用**: 安装包<2GB，运行时内存<4GB
- **文档完整性**: 用户手册、API文档、故障排除指南

---

*注：本文档基于当前代码实现状态编写，部分功能仍在开发完善中。建议在以下位置添加相应图片以增强文档可读性：*

1. **第3.1节后**: 添加系统架构图，展示硬件假设和环境要求
2. **第3.3节后**: 添加约束影响的流程图
3. **第4.1节**: 添加技术栈架构图
4. **第4.6节**: 添加YOLOP模型结构图和多任务学习示意图
5. **第5.1节**: 添加系统功能模块图和用户界面截图
6. **第5.2节**: 添加性能测试结果图表和对比分析图
