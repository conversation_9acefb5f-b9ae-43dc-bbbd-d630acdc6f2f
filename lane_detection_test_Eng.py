import os
import numpy as np
import warnings
from ultralytics import YOLO
import onnxruntime as ort
import torch
from lib.core.general import non_max_suppression
import cv2
import PySimpleGUIQt as sg  # 使用 Qt 后端，不依赖 Tcl/Tk

def process_frame(frame):
    """在这里实现你的图像处理逻辑"""
    # 示例处理：转换为灰度图像
    processed = process_single_image(frame)
    return processed

def main():
    #messagebox.showinfo("信息", "main() 函数被调用了！")
    # 打开默认摄像头（通常索引为0）
    cap = cv2.VideoCapture(0)

    if not cap.isOpened():
        print("Failed to open camera")
        return

    # 设置摄像头分辨率（可选）
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    # 创建窗口
    cv2.namedWindow('original vid', cv2.WINDOW_NORMAL)
    cv2.namedWindow('processed vid', cv2.WINDOW_NORMAL)

    try:
        while True:
            # 读取一帧
            ret, frame = cap.read()

            if not ret:
                print("failed to get frame")
                break

            # 显示原始帧
            cv2.imshow('original vid', frame)

            # 处理帧
            processed_frame = process_frame(frame)

            # 显示处理后的帧
            cv2.imshow('processed vid', processed_frame)

            # 监听键盘事件
            key = cv2.waitKey(1)

            # 按ESC键退出（ASCII码为27）
            if key == 27:  # ESC键的ASCII码
                #print("用户按下了ESC键，程序即将退出...")
                break

            # 也可以保留原来的q键退出功能（可选）
            elif key & 0xFF == ord('q'):
                #print("用户按下了q键，程序即将退出...")
                break

    finally:
        # 释放资源
        cap.release()
        cv2.destroyAllWindows()
        #print("摄像头已释放，窗口已关闭")

class YOLOP_ONNX:
    def __init__(self, weight_path="YOLOP-main/weights/yolop-640-640.onnx"):
        """
        初始化YOLOP ONNX模型
        :param weight_path: ONNX模型文件路径
        """
        ort.set_default_logger_severity(4)
        # 获取可用提供者
        providers = ort.get_available_providers()

        # 优先使用CUDA（如果可用），否则使用CPU
        if 'CUDAExecutionProvider' in providers:
            provider = ['CUDAExecutionProvider']
            #print("Using CUDA for ONNX inference")
        else:
            provider = ['CPUExecutionProvider']
            #print("Using CPU for ONNX inference")

        # 创建会话时指定提供者
        self.ort_session = ort.InferenceSession(weight_path, providers=provider)

    def _resize_unscale(self, img, new_shape=(640, 640), color=114):
        """
        图像预处理：缩放和填充
        """
        shape = img.shape[:2]  # 当前形状 [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        canvas = np.zeros((new_shape[0], new_shape[1], 3))
        canvas.fill(color)
        # 计算缩放比例
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])

        # 计算填充
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))  # w,h
        new_unpad_w, new_unpad_h = new_unpad
        pad_w, pad_h = new_shape[1] - new_unpad_w, new_shape[0] - new_unpad_h  # wh padding

        dw = pad_w // 2  # 将填充分成两边
        dh = pad_h // 2

        if shape[::-1] != new_unpad:  # 缩放
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_AREA)

        canvas[dh:dh + new_unpad_h, dw:dw + new_unpad_w, :] = img

        return canvas, r, dw, dh, new_unpad_w, new_unpad_h

    def process_image(self, img_bgr):
        """
        处理输入图像并返回检测结果
        :param img_bgr: 输入的BGR格式图像(numpy数组)
        :return: 包含检测结果的字典:
                 {
                     'detection': 带检测框的图像,
                     'da_seg': 可行驶区域分割掩码,
                     'll_seg': 车道线分割掩码,
                     'merged': 合并所有结果的可视化图像
                 }
        """
        #img_bgr = cv2.imread(img_bgr) #删这个

        height, width, _ = img_bgr.shape

        # 转换为RGB
        img_rgb = img_bgr[:, :, ::-1].copy()

        # 缩放和填充
        canvas, r, dw, dh, new_unpad_w, new_unpad_h = self._resize_unscale(img_rgb, (640, 640))

        # 归一化
        img = canvas.copy().astype(np.float32)
        img /= 255.0
        img[:, :, 0] -= 0.485
        img[:, :, 1] -= 0.456
        img[:, :, 2] -= 0.406
        img[:, :, 0] /= 0.229
        img[:, :, 1] /= 0.224
        img[:, :, 2] /= 0.225

        img = img.transpose(2, 0, 1)
        img = np.expand_dims(img, 0)  # (1, 3, 640, 640)

        # 推理
        det_out, da_seg_out, ll_seg_out = self.ort_session.run(
            ['det_out', 'drive_area_seg', 'lane_line_seg'],
            input_feed={"images": img}
        )

        # 处理检测结果
        det_out = torch.from_numpy(det_out).float()
        #boxes = non_max_suppression(det_out)[0]  # [n,6] [x1,y1,x2,y2,conf,cls]
        conf_thres = 0.6  # 自定义置信度阈值
        iou_thres = 0.65  # IoU阈值（可选调整）
        boxes = non_max_suppression(det_out, conf_thres=conf_thres, iou_thres=iou_thres)[0]
        boxes = boxes.cpu().numpy().astype(np.float32)

        # 准备返回结果
        results = {
            'detection': None,
            'da_seg': None,
            'll_seg': None,
            'merged': None
        }

        # 处理检测框
        img_det = img_bgr.copy()
        if boxes.shape[0] > 0:
            # 缩放坐标到原始尺寸
            boxes[:, 0] -= dw
            boxes[:, 1] -= dh
            boxes[:, 2] -= dw
            boxes[:, 3] -= dh
            boxes[:, :4] /= r

            for i in range(boxes.shape[0]):
                x1, y1, x2, y2, conf, label = boxes[i]
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                img_det = cv2.rectangle(img_det, (x1, y1), (x2, y2), (0, 255, 0), 2)

        results['detection'] = img_det

        # 处理分割掩码
        da_seg_out = da_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]
        ll_seg_out = ll_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]

        da_seg_mask = np.argmax(da_seg_out, axis=1)[0]  # (?,?) (0|1)
        ll_seg_mask = np.argmax(ll_seg_out, axis=1)[0]  # (?,?) (0|1)

        # ========== 改进的中间线计算：多项式拟合 ==========
        def calculate_center_line(left_points, right_points, y_range=None, step=10, poly_order=2):
            """
            通过多项式拟合计算中间线
            :param left_points: 左车道线点集 [(x,y),...]
            :param right_points: 右车道线点集 [(x,y),...]
            :param y_range: 垂直范围 (y_start, y_end)，None表示自动计算
            :param step: 采样步长(像素)
            :param poly_order: 多项式阶数
            :return: 中间线点集
            """
            if len(left_points) < 3 or len(right_points) < 3:
                return []

            # 转换为numpy数组
            left_arr = np.array(left_points)
            right_arr = np.array(right_points)

            # 多项式拟合左右车道线
            with warnings.catch_warnings():
                warnings.simplefilter('ignore', RuntimeWarning)  # 忽略拟合警告
                left_fit = np.polyfit(left_arr[:, 1], left_arr[:, 0], poly_order)
                right_fit = np.polyfit(right_arr[:, 1], right_arr[:, 0], poly_order)

            # 计算采样范围
            if y_range is None:
                y_min = max(np.min(left_arr[:, 1]), np.min(right_arr[:, 1]))  # 最低点
                y_max = min(np.max(left_arr[:, 1]), np.max(right_arr[:, 1]))  # 最高点

            else:
                y_min, y_max = y_range

            y_min = min(np.min(left_arr[:, 1]), np.min(right_arr[:, 1]))  # 最低点
            y_max = max(np.max(left_arr[:, 1]), np.max(right_arr[:, 1]))  # 最高点

            # 生成采样点y坐标
            y_samples = np.arange(y_max, y_min, -step)

            # 计算中间线多项式系数（左右系数的平均值）
            center_fit = (left_fit + right_fit) / 2

            # 从多项式计算中间线点
            center_points = []
            for y in y_samples:
                # 计算左右线的x值
                x_left = np.polyval(left_fit, y)
                x_right = np.polyval(right_fit, y)

                # 计算中间线x值（限制在左右线之间）
                x_center = np.polyval(center_fit, y)
                x_center = np.clip(x_center, x_left, x_right)

                center_points.append((x_center, y))

            return center_points



        # ========== 新增：基于射线法的车道线检测 ==========
        def calculate_lane_lines_by_rays(mask, ray_step=4, search_range=150):
            """
            基于射线法的车道线检测
            :param mask: 车道线二值掩码
            :param ray_step: 垂直方向射线间隔(像素)
            :param search_range: 水平搜索范围(像素)
            :return: 左右车道线点集、中间线点集
            """
            h, w = mask.shape
            center_x = w // 2
            left_points = []
            right_points = []

            # 从底部向上发射射线
            for y in range(h - 1, 0, -ray_step):
                if center_x - search_range <1 or center_x + search_range>w-1:
                    center_x =w // 2
                # 向左搜索左车道线
                left_x = None
                for x in range(center_x, max(0, center_x - search_range), -1):
                    if mask[y, x] > 0:
                        left_x = x
                        break

                # 向右搜索右车道线
                right_x = None
                for x in range(center_x, min(w, center_x + search_range)):
                    if mask[y, x] > 0:
                        right_x = x
                        break

                # 记录找到的点
                if left_x is not None:
                    left_points.append((left_x, y))
                if right_x is not None:
                    right_points.append((right_x, y))

                # 动态调整中心位置（基于上一对找到的点）
                if left_x is not None and right_x is not None:
                    center_x = (left_x + right_x) // 2
                elif left_x is not None:
                    center_x = left_x + search_range  # 预估右车道线位置
                elif right_x is not None:
                    center_x = right_x - search_range  # 预估左车道线位置

            # ==== 多项式拟合计算中间线 ====
            def fit_poly(points, order):
                """拟合点集为多项式（y为自变量）"""
                if len(points) < order + 1:
                    return np.zeros(order + 1)
                arr = np.array(points)
                return np.polyfit(arr[:, 1], arr[:, 0], order)

            # 拟合左右车道线
            left_coeffs = fit_poly(left_points, 2)
            right_coeffs = fit_poly(right_points, 2)

            # 补齐系数（确保相同长度）
            max_len = max(len(left_coeffs), len(right_coeffs))
            left_coeffs = np.pad(left_coeffs, (0, max_len - len(left_coeffs)), 'constant')
            right_coeffs = np.pad(right_coeffs, (0, max_len - len(right_coeffs)), 'constant')

            # 计算中间线系数（平均）
            center_coeffs = (left_coeffs + right_coeffs) / 2

            # 生成中间线点集
            y_values = np.linspace(0, h - 1, 20)
            center_x = np.polyval(center_coeffs[::-1], y_values)  # 注意系数顺序
            center_points = np.column_stack((center_x, y_values)).astype(int).tolist()

            return left_points, right_points, center_points


            # ========== 新增：多项式拟合车道线 ==========

        def fit_lane_polynomials(left_points, right_points, poly_order=2):
            """
            多项式拟合车道线并计算中间线
            :param left_points: 左车道线点集 [(x,y),...]
            :param right_points: 右车道线点集 [(x,y),...]
            :param poly_order: 多项式阶数
            :return: 左、右、中间线的多项式系数
            """
            # 转换为numpy数组
            left_arr = np.array(left_points)
            right_arr = np.array(right_points)

            # 拟合左右车道线多项式（y为自变量，x为因变量）
            if len(left_arr) >= poly_order + 1:
                left_coeffs = np.polyfit(left_arr[:, 1], left_arr[:, 0], poly_order)
            else:
                left_coeffs = np.zeros(poly_order + 1)

            if len(right_arr) >= poly_order + 1:
                right_coeffs = np.polyfit(right_arr[:, 1], right_arr[:, 0], poly_order)
            else:
                right_coeffs = np.zeros(poly_order + 1)

            # 补齐系数到相同长度（最高次项在前）
            max_len = max(len(left_coeffs), len(right_coeffs))
            left_coeffs = np.pad(left_coeffs, (0, max_len - len(left_coeffs)), 'constant')
            right_coeffs = np.pad(right_coeffs, (0, max_len - len(right_coeffs)), 'constant')

            # 计算中间线系数（取平均值）
            center_coeffs = (left_coeffs + right_coeffs) / 2

            return left_coeffs, right_coeffs, center_coeffs

        left_lane, right_lane, center_lane = calculate_lane_lines_by_rays(ll_seg_mask)
        # 计算中间线（使用多项式拟合方法）
        h, w = ll_seg_mask.shape
        center_lane = calculate_center_line(
            left_lane,
            right_lane,
            y_range=(h * 0.3, h),  # 只拟合图像下半部分
            step=5,  # 采样步长
            poly_order=2  # 二次多项式
        )
        left_coeffs, right_coeffs, center_coeffs = fit_lane_polynomials(left_lane, right_lane)
        # 生成拟合点集
        y_values = np.linspace(0, ll_seg_mask.shape[0] - 1, 20)  # 在垂直方向采样20个点
        # 创建彩色分割区域
        color_area = np.zeros((new_unpad_h, new_unpad_w, 3), dtype=np.uint8)
        color_area[da_seg_mask == 1] = [0, 255, 0]  # 可行驶区域 - 绿色
        color_area[ll_seg_mask == 1] = [255, 0, 0]  # 车道线 - 红色

        # 绘制车道线（修改原代码）
        # 原始车道线检测结果（红色）
        ll_seg_color = np.zeros((new_unpad_h, new_unpad_w, 3), dtype=np.uint8)
        ll_seg_color[ll_seg_mask == 1] = [255, 0, 0]
        color_area = cv2.addWeighted(color_area, 1.0, ll_seg_color, 1.0, 0)
        # for lane in center_lane:
        #     print(f"{lane[0]},{lane[1]}")
        #print(f"{len(left_lane)},  {len(right_lane)},  {len(center_lane)}")
        # 绘制推算的车道线（新增）
        if len(left_lane) > 1:
            left_lane = np.array(left_lane, np.int32)
            cv2.polylines(color_area, [left_lane], False, (255, 255, 0), 2)  # 左车道线(青色)

        if len(right_lane) > 1:
            right_lane = np.array(right_lane, np.int32)
            cv2.polylines(color_area, [right_lane], False, (0, 255, 255), 2)  # 右车道线(黄色)

        if len(center_lane) > 1:
            center_lane = np.array(center_lane, np.int32)
            cv2.polylines(color_area, [center_lane], False, (255, 0, 255), 2)  # 中间线(紫色


        # 转换为BGR
        color_seg = color_area[..., ::-1]
        color_mask = np.mean(color_seg, 2)

        # 合并结果
        img_merge = canvas[dh:dh + new_unpad_h, dw:dw + new_unpad_w, :]
        img_merge = img_merge[:, :, ::-1]
        img_merge[color_mask != 0] = img_merge[color_mask != 0] * 0.5 + color_seg[color_mask != 0] * 0.5
        img_merge = img_merge.astype(np.uint8)

        # # 添加检测框到合并图像
        # if boxes.shape[0] > 0:
        #     for i in range(boxes.shape[0]):
        #         x1, y1, x2, y2, conf, label = boxes[i]
        #         x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
        #         img_merge = cv2.rectangle(img_merge, (x1, y1), (x2, y2), (0, 255, 0), 2)
        # 修改后的检测框添加部分
        if boxes.shape[0] > 0:
            # 创建一个与img_merge相同尺寸的画布用于绘制检测框
            boxes_canvas = np.zeros_like(img_merge)

            # 计算缩放后的框坐标
            scale_w = new_unpad_w / width
            scale_h = new_unpad_h / height

            for i in range(boxes.shape[0]):
                # 原始坐标
                x1, y1, x2, y2, conf, label = boxes[i]
                # 缩放坐标
                x1 = int(x1 * scale_w)
                y1 = int(y1 * scale_h)
                x2 = int(x2 * scale_w)
                y2 = int(y2 * scale_h)
                # 在boxes_canvas上绘制检测框
                boxes_canvas = cv2.rectangle(boxes_canvas, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 将检测框叠加到合并图像上
            img_merge = cv2.addWeighted(img_merge, 1.0, boxes_canvas, 1.0, 0)

        # 处理分割掩码
        da_seg_out = da_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]
        ll_seg_out = ll_seg_out[:, :, dh:dh + new_unpad_h, dw:dw + new_unpad_w]

        # 确保掩码是uint8类型且为2维
        da_seg_mask = np.argmax(da_seg_out, axis=1)[0].astype(np.uint8)  # shape: (H, W)
        ll_seg_mask = np.argmax(ll_seg_out, axis=1)[0].astype(np.uint8)  # shape: (H, W)

        # 调整大小
        da_seg_mask = cv2.resize((da_seg_mask * 255).astype(np.uint8), (width, height), interpolation=cv2.INTER_LINEAR)
        ll_seg_mask = cv2.resize((ll_seg_mask * 255).astype(np.uint8), (width, height), interpolation=cv2.INTER_LINEAR)

        # 在返回结果中添加车道线信息
        results['left_lane'] = left_lane
        results['right_lane'] = right_lane
        results['center_lane'] = center_lane
        results['da_seg'] = da_seg_mask
        results['ll_seg'] = ll_seg_mask
        results['merged'] = img_merge


        #import cv2

        # 加载模型
        model = YOLO("YOLOv8_GUI_For_Traffic_Sign_Detection-main/ptfolder/best.pt")
        model.to('cuda' if torch.cuda.is_available() else 'cpu')  # 自动选择设备


        # 检测图片
        results = model.predict(
            source=results['merged'],
            conf=0.4,  # 提高置信度阈值减少误检
            save=False,  # 保存结果到`runs/detect/predict/`
            show_labels=True,
            show_conf=True
        )

        # 显示结果
        res_plotted = results[0].plot()

        return res_plotted

def process_single_image(img_file):
    detector = YOLOP_ONNX("YOLOP-main/weights/yolop-640-640.onnx")
    results = detector.process_image(img_file)#这是直接的图
    return results

def frames_to_video(frames, original_video_path, output_path="output.mp4"):
    """
    将帧数组合成为视频，保留原视频的分辨率和FPS
    :param frames: List[np.ndarray], 处理后的帧数组（BGR格式）
    :param original_video_path: 原视频路径（用于获取元数据）
    :param output_path: 输出视频路径
    """
    # 从原视频获取分辨率、FPS等元数据
    cap = cv2.VideoCapture(original_video_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap.release()

    # 初始化视频写入器（MP4格式）
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 或 'avc1'（H.264编码）
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # 逐帧写入
    for frame in frames:
        # 确保帧的分辨率与原始一致（可自动调整）
        if frame.shape[0] != height or frame.shape[1] != width:
            frame = cv2.resize(frame, (width, height))
        out.write(frame)  # 写入帧（BGR格式）

    out.release()
    sg.popup(f"vid saved to : {output_path}!", title="Hint")
    #print(f"视频已保存至: {output_path}")

def process_video(input_video, output_path):
    sg.popup_no_wait("Initializing models", title="Hint",auto_close=True,auto_close_duration=8)
    frames = video_to_frames_array(input_video)
    frames_to_video(frames, input_video, output_path)


def video_to_frames_array(video_path):
    """
    将视频分解为帧的 numpy 数组列表
    :param video_path: 视频文件路径
    :return: List[np.ndarray], 每个元素为一帧的BGR图像数组 (H, W, 3)
    """

    cap = cv2.VideoCapture(video_path)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    frames = []
    window1 = sg.Window("progress", layout1,finalize=True)

    while cap.isOpened():

        ret, frame = cap.read()
        if not ret:  # 读取结束
            break
        detected_frame = process_single_image(frame)

        frames.append(detected_frame)  # frame是numpy数组，形状为 (H, W, 3)

        i = len(frames)/frame_count
        window1["-PROGRESS_TEXT-"].update(f"progress：{i*100}%")
        window1.refresh()  # 强制刷新窗口

    cap.release()
    window1.close()
    return frames


def browse_input_file():
    # 选择输入文件（MP4）
    file_path = sg.popup_get_file(
        "select input MP4 vid",
        file_types=(("MP4 file", "*.mp4"),),
        no_window=True
    )
    return file_path if file_path else None


def browse_output_path(input_video):
    # 默认输出文件名（输入文件名 + "_output.mp4"）
    default_name = os.path.splitext(os.path.basename(input_video))[0] + "_output.mp4"

    # 弹出窗口让用户选择输出目录和文件名
    layout = [
        [sg.Text("output path: "), sg.Input(key="-FOLDER-"), sg.FolderBrowse("browse")],
        [sg.Text("output name: "), sg.Input(default_name, key="-NAME-")],
        [sg.Button("confirm"), sg.Button("cancel")]
    ]

    window = sg.Window("Select output path", layout)

    while True:
        event, values = window.read()
        if event in (sg.WINDOW_CLOSED, "cancel"):
            output_path = None
            break
        elif event == "confirm":
            folder = values["-FOLDER-"]
            name = values["-NAME-"]
            if not folder or not name:
                sg.popup_error("Please name and select output path!")
                continue
            output_path = os.path.join(folder, name)
            break

    window.close()
    return output_path

# 主窗口布局
layout = [
    [
        sg.Button("1. Select input vid", key="-INPUT-", size=(20, 3)),
        sg.Button("2. Real-time detction", key="-MAIN-", size=(20, 3))
    ],
    [sg.Text(" ", text_color="gray", justification="center")]
]
layout1 = [
    [sg.Text("progress：0%", key="-PROGRESS_TEXT-")],
    #[sg.ProgressBar(100, orientation="h", size=(30, 20), key="-PROGRESS_BAR-")],
]
# 创建主窗口（启用拖放）
window = sg.Window("vid processor", layout, finalize=True)
window["-INPUT-"].Widget.setAcceptDrops(True)  # 允许拖放

# 事件循环
input_video = None
while True:
    event, values = window.read()

    # 关闭窗口
    if event == sg.WINDOW_CLOSED:
        break

    # 按钮2：调用 main()
    elif event == "-MAIN-":
        main()

    # 按钮1：选择输入文件（点击）
    elif event == "-INPUT-":
        input_video = browse_input_file()
        if input_video:
            output_path = browse_output_path(input_video)
            if output_path:
                process_video(input_video, output_path)

    # 按钮1：拖放文件
    elif event == "-INPUT-+DROP-":
        dropped_files = values["-INPUT-+DROP-"]
        if dropped_files:
            input_video = dropped_files[0]
            if input_video.lower().endswith(".mp4"):
                output_path = browse_output_path(input_video)
                if output_path:
                    process_video(input_video, output_path)
            else:
                sg.popup_error("Please drag .MP4!")

window.close()